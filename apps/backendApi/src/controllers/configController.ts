// /backendApi/controllers/configController.ts
import { Request, Response, NextFunction } from 'express';
import { cacheManager } from '@backendApiUtils/cacheManager';
import { db, getTable } from '@insolence-rgs/db';

const isDebug = process.env.LOG_LEVEL === 'debug';

/**
 * Map database game codes to frontend game IDs
 */
function mapDatabaseGameToFrontend(gameCode: string): string {
  const mapping: Record<string, string> = {
    'ins_mines': 'ins_mines',
    'ins_lootbox': 'ins_lootbox_base',
    'ins_lootbox2': 'ins_lootbox_new',
    'ins_crash': 'ins_crash',
    'ins_testgame': 'ins_testgame'
  };

  return mapping[gameCode] || gameCode;
}

/**
 * Get default description for games
 */
function getDefaultDescription(gameId: string): string {
  const descriptions: Record<string, string> = {
    'ins_mines': '2D strategy game with configurable difficulty and real-time multipliers',
    'ins_lootbox_base': '3D Lootbox Game with modular architecture, enhanced performance, and mobile optimization',
    'ins_lootbox_new': 'New variant of the lootbox game for independent customization',
    'ins_lootbox_third': 'Third variant of the lootbox game',
    'ins_crash': 'High-stakes crash game with multiplier mechanics',
    'ins_testgame': 'Test slot game for development purposes'
  };

  return descriptions[gameId] || 'Casino game';
}

/**
 * Get game colors for consistent theming
 */
function getGameColor(gameId: string): string {
  const colors: Record<string, string> = {
    'ins_lootbox_base': '#ff6600',
    'ins_lootbox_new': '#00cc66',
    'ins_lootbox_third': '#6600ff',
    'ins_mines': '#ff3366',
    'ins_crash': '#ff9900',
    'ins_testgame': '#3366ff'
  };

  return colors[gameId] || '#666666';
}

/**
 * Get game icons
 */
function getGameIcon(gameId: string): string {
  const icons: Record<string, string> = {
    'ins_lootbox_base': '🎁',
    'ins_lootbox_new': '🎁',
    'ins_lootbox_third': '🎁',
    'ins_mines': '💣',
    'ins_crash': '🚀',
    'ins_testgame': '🎰'
  };

  return icons[gameId] || '🎮';
}

/**
 * Get game category based on game type
 */
function getGameCategory(gameType: string): '2D' | '3D' | 'VR' | 'AR' {
  const categoryMap: Record<string, '2D' | '3D' | 'VR' | 'AR'> = {
    'lootbox': '3D',
    'strategy': '2D',
    'crash': '2D',
    'slot': '2D'
  };

  return categoryMap[gameType] || '2D';
}

/**
 * Get game technology based on engine
 */
function getGameTechnology(engine: string): string {
  const techMap: Record<string, string> = {
    'three-js': 'Three.js + React',
    'engine-v1': 'React + TypeScript',
    'engine-v2': 'React + Canvas',
    'crash-engine': 'React + WebGL'
  };

  return techMap[engine] || 'React + TypeScript';
}

/**
 * Get game difficulty
 */
function getGameDifficulty(gameId: string): 'Easy' | 'Medium' | 'Hard' | 'Expert' {
  const difficultyMap: Record<string, 'Easy' | 'Medium' | 'Hard' | 'Expert'> = {
    'ins_lootbox_base': 'Easy',
    'ins_lootbox_new': 'Easy',
    'ins_lootbox_third': 'Easy',
    'ins_mines': 'Medium',
    'ins_crash': 'Hard',
    'ins_testgame': 'Easy'
  };

  return difficultyMap[gameId] || 'Medium';
}

/**
 * Get game features
 */
function getGameFeatures(gameId: string): string[] {
  const featuresMap: Record<string, string[]> = {
    'ins_mines': ['Configurable Grid', 'Real-time API', 'Multipliers', 'Strategy'],
    'ins_lootbox_base': ['3D Graphics', 'Animations', 'Rarity System', 'Mobile Optimized'],
    'ins_lootbox_new': ['3D Graphics', 'Animations', 'Rarity System', 'New Mechanics'],
    'ins_lootbox_third': ['3D Graphics', 'Animations', 'Rarity System', 'Third Variant'],
    'ins_crash': ['Real-time Multipliers', 'Auto-cashout', 'Live Statistics'],
    'ins_testgame': ['Test Features', 'Development Mode']
  };

  return featuresMap[gameId] || [];
}

/**
 * Check if game is new
 */
function isNewGame(gameId: string): boolean {
  const newGames = ['ins_lootbox_new', 'ins_lootbox_third'];
  return newGames.includes(gameId);
}

/**
 * Get game version
 */
function getGameVersion(gameId: string): string {
  const versionMap: Record<string, string> = {
    'ins_lootbox_base': '2.0',
    'ins_lootbox_new': '1.0',
    'ins_lootbox_third': '1.0',
    'ins_mines': '1.5',
    'ins_crash': '1.0',
    'ins_testgame': '0.1'
  };

  return versionMap[gameId] || '1.0';
}

/**
 * Get operator-specific games configuration
 * POST /api/config/operator-games
 */
export const getOperatorGames = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { operator_id } = req.body;

    if (!operator_id) {
      res.status(400).json({
        error: 'MISSING_OPERATOR_ID',
        message: 'operator_id is required'
      });
      return;
    }

    if (isDebug) {
      console.log('[ConfigController] 🎮 Getting games for operator:', operator_id);
    }

    // Handle demo_server with default games
    if (operator_id === 'demo_server') {
      // Get games from database and map to frontend IDs
      const demoGamesQuery = `
        SELECT game_code, display_name, description, game_type, engine, status
        FROM config.games
        WHERE status = 'active' AND globally_enabled = true
        AND game_code IN ('ins_mines', 'ins_lootbox', 'ins_lootbox2', 'ins_crash')
        ORDER BY display_name
      `;

      const demoGamesResult = await db.query(demoGamesQuery);
      const demoGames = demoGamesResult.rows.map(game => mapDatabaseGameToFrontend(game.game_code));

      res.status(200).json({
        success: true,
        operator_id,
        games: demoGames,
        total_games: demoGames.length,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Check if operator exists in cache
    const operatorDetails = cacheManager.getOperatorDetails(operator_id, 'production');
    if (!operatorDetails) {
      res.status(404).json({
        error: 'OPERATOR_NOT_FOUND',
        message: `Operator ${operator_id} not found or inactive`
      });
      return;
    }

    // Get operator-specific game permissions from database
    const gamePermissionsQuery = `
      SELECT DISTINCT g.game_code, g.name, g.status
      FROM config.games g
      LEFT JOIN config.operator_game_settings ogs ON g.game_code = ogs.game_code AND ogs.operator_id = $1
      WHERE g.status = 'active' 
        AND (ogs.is_enabled = true OR ogs.operator_id IS NULL)
      ORDER BY g.game_code
    `;

    const gamePermissionsResult = await db.query(gamePermissionsQuery, [operator_id]);
    const allowedGames = gamePermissionsResult.rows.map(row => row.game_code);

    if (isDebug) {
      console.log(`[ConfigController] ✅ Found ${allowedGames.length} games for operator ${operator_id}`);
    }

    res.status(200).json({
      success: true,
      operator_id,
      games: allowedGames,
      total_games: allowedGames.length,
      game_details: gamePermissionsResult.rows,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ConfigController] ❌ Error getting operator games:', error);
    
    res.status(500).json({
      error: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to retrieve operator games configuration',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get game configuration for specific game
 * POST /api/config/game-details
 */
export const getGameDetails = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { game_code, operator_id } = req.body;

    if (!game_code) {
      res.status(400).json({
        error: 'MISSING_GAME_CODE',
        message: 'game_code is required'
      });
      return;
    }

    if (isDebug) {
      console.log('[ConfigController] 🎯 Getting game details:', { game_code, operator_id });
    }

    // Get base game configuration
    const gameQuery = `
      SELECT 
        g.*,
        ogs.bet_limits,
        ogs.rtp_settings,
        ogs.is_enabled as operator_enabled
      FROM config.games g
      LEFT JOIN config.operator_game_settings ogs ON g.game_code = ogs.game_code AND ogs.operator_id = $2
      WHERE g.game_code = $1
    `;

    const gameResult = await db.query(gameQuery, [game_code, operator_id || 'demo_server']);

    if (!gameResult.rowCount) {
      res.status(404).json({
        error: 'GAME_NOT_FOUND',
        message: `Game ${game_code} not found`
      });
      return;
    }

    const gameDetails = gameResult.rows[0];

    res.status(200).json({
      success: true,
      game_code,
      operator_id: operator_id || 'demo_server',
      game_details: gameDetails,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ConfigController] ❌ Error getting game details:', error);
    
    res.status(500).json({
      error: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to retrieve game details',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * Get all available games (for lobby discovery)
 * GET /api/config/all-games
 */
export const getAllGames = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (isDebug) {
      console.log('[ConfigController] 📋 Getting all available games');
    }

    // Get all active games from database with proper mapping
    const gamesQuery = `
      SELECT
        game_code,
        display_name,
        description,
        game_type,
        engine,
        status,
        globally_enabled,
        created_at,
        updated_at
      FROM config.games
      WHERE status = 'active' AND globally_enabled = true
      ORDER BY display_name
    `;

    const gamesResult = await db.query(gamesQuery);

    // Map database games to frontend game IDs and add metadata
    const gamesWithMetadata = gamesResult.rows.map(game => {
      // Map database game codes to frontend game IDs
      const frontendGameId = mapDatabaseGameToFrontend(game.game_code);

      return {
        id: frontendGameId,
        game_code: game.game_code,
        name: game.display_name,
        description: game.description || getDefaultDescription(frontendGameId),
        game_type: game.game_type,
        engine: game.engine,
        status: game.status,
        url: `/${frontendGameId}.html`,
        color: getGameColor(frontendGameId),
        icon: getGameIcon(frontendGameId),
        category: getGameCategory(game.game_type),
        technology: getGameTechnology(game.engine),
        difficulty: getGameDifficulty(frontendGameId),
        features: getGameFeatures(frontendGameId),
        isNew: isNewGame(frontendGameId),
        version: getGameVersion(frontendGameId),
        created_at: game.created_at,
        updated_at: game.updated_at
      };
    });

    res.status(200).json({
      success: true,
      games: gamesWithMetadata,
      total_games: gamesWithMetadata.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[ConfigController] ❌ Error getting all games:', error);
    
    res.status(500).json({
      error: 'INTERNAL_SERVER_ERROR',
      message: 'Failed to retrieve games list',
      timestamp: new Date().toISOString()
    });
  }
};
