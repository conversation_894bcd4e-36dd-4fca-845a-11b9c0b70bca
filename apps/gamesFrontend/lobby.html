<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Insolence Games Lobby - Dynamic Game Discovery</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Dynamic multi-game lobby with Redis cache integration and casino-grade session management" />
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            min-height: 100vh;
        }
        
        #root {
            width: 100%;
            height: 100vh;
        }
        
        /* Loading animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Fallback styles for when React hasn't loaded */
        .lobby-fallback {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            color: white;
            text-align: center;
        }
        
        .lobby-fallback h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .lobby-fallback p {
            font-size: 1.2rem;
            opacity: 0.7;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div id="root">
        <!-- Fallback content while React loads -->
        <div class="lobby-fallback">
            <h1>🎮 Insolence Games Lobby</h1>
            <p>Loading dynamic game discovery...</p>
            <div class="loading-spinner"></div>
        </div>
    </div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Error handling script -->
    <script>
        window.addEventListener('error', function(e) {
            console.error('Lobby loading error:', e.error);
            
            // Show fallback error message
            const root = document.getElementById('root');
            if (root) {
                root.innerHTML = `
                    <div class="lobby-fallback">
                        <h1>🎮 Insolence Games Lobby</h1>
                        <p style="color: #ff6666;">Failed to load dynamic lobby</p>
                        <p style="font-size: 1rem; opacity: 0.8;">
                            Error: ${e.error?.message || 'Unknown error'}
                        </p>
                        <button 
                            onclick="window.location.reload()" 
                            style="
                                padding: 12px 24px;
                                font-size: 1rem;
                                border-radius: 20px;
                                background-color: #4caf50;
                                color: white;
                                border: none;
                                cursor: pointer;
                                margin-top: 20px;
                            "
                        >
                            🔄 Retry
                        </button>
                        <div style="margin-top: 30px;">
                            <a href="/ins_mines.html" style="color: #66bb6a; text-decoration: none; margin: 0 10px;">🎯 Mines Game</a>
                            <a href="/ins_lootbox_base.html" style="color: #66bb6a; text-decoration: none; margin: 0 10px;">🎁 Lootbox Base</a>
                            <a href="/ins_lootbox_new.html" style="color: #66bb6a; text-decoration: none; margin: 0 10px;">🎁 Lootbox New</a>
                        </div>
                    </div>
                `;
            }
        });
        
        // Add session parameter detection
        window.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const hasSessionParams = urlParams.has('session_id') || urlParams.has('operator_id');
            
            if (hasSessionParams) {
                console.log('🔐 Operator session parameters detected:', Object.fromEntries(urlParams));
            } else {
                console.log('🎮 Demo mode - no operator session parameters');
            }
        });
    </script>
</body>
</html>
