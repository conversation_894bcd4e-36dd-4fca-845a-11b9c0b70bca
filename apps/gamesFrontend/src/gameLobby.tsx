import React, { useState, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { DynamicGameDiscoveryService, DynamicGameInfo } from './services/dynamicGameDiscovery';
import { LobbySessionManager } from './services/lobbySessionManager';

const GameLobby = () => {
    const [games, setGames] = useState<DynamicGameInfo[]>([]);
    const [loading, setLoading] = useState(true);
    const [sessionAuthenticated, setSessionAuthenticated] = useState(false);
    const [sessionData, setSessionData] = useState<any>(null);
    const [operatorGames, setOperatorGames] = useState<string[]>([]);
    const [discoverySource, setDiscoverySource] = useState<'api' | 'fallback'>('api');
    const [error, setError] = useState<string | null>(null);

    const lobbySessionManager = LobbySessionManager.getInstance();
    const gameDiscoveryService = DynamicGameDiscoveryService.getInstance();

    useEffect(() => {
        const initializeLobby = async () => {
            try {
                // Initialize session and authenticate
                const authenticated = await lobbySessionManager.authenticateSession();
                setSessionAuthenticated(authenticated);
                setSessionData(lobbySessionManager.getSessionData());

                // Load operator-specific games
                const allowedGames = await lobbySessionManager.loadOperatorGames();
                setOperatorGames(allowedGames);

                // Discover available games using dynamic service
                const discoveryResult = await gameDiscoveryService.discoverGames();

                if (!discoveryResult.success) {
                    throw new Error(discoveryResult.error || 'Failed to discover games');
                }

                setDiscoverySource(discoveryResult.source);

                // Filter games based on operator permissions
                const filteredGames = allowedGames.length > 0
                    ? discoveryResult.games.filter(game => allowedGames.includes(game.id))
                    : discoveryResult.games;

                setGames(filteredGames);

                if (discoveryResult.source === 'fallback') {
                    setError('Using fallback games - API connection failed');
                }

                console.log(`✅ Lobby initialized with ${filteredGames.length} games from ${discoveryResult.source}`);
            } catch (error) {
                console.error('❌ Failed to initialize lobby:', error);
                setError(error instanceof Error ? error.message : 'Unknown error');
            } finally {
                setLoading(false);
            }
        };

        initializeLobby();
    }, []);

    const handleEnterGame = (gameId: string) => {
        try {
            // Generate game launch URL with session context
            const launchUrl = lobbySessionManager.generateGameLaunchUrl(gameId);
            console.log(`🚀 Launching game ${gameId} with session context:`, launchUrl);
            window.location.href = launchUrl;
        } catch (error) {
            console.error(`❌ Failed to launch game ${gameId}:`, error);
            // Fallback to basic game launch
            window.location.href = `/${gameId}.html`;
        }
    };

    const handleRefreshGames = async () => {
        setLoading(true);
        setError(null);

        try {
            // Clear cache and reload games
            gameDiscoveryService.clearCache();

            const discoveryResult = await gameDiscoveryService.discoverGames();

            if (!discoveryResult.success) {
                throw new Error(discoveryResult.error || 'Failed to refresh games');
            }

            setDiscoverySource(discoveryResult.source);

            // Filter games based on operator permissions
            const filteredGames = operatorGames.length > 0
                ? discoveryResult.games.filter(game => operatorGames.includes(game.id))
                : discoveryResult.games;

            setGames(filteredGames);

            if (discoveryResult.source === 'fallback') {
                setError('Using fallback games - API connection failed');
            }

            console.log(`✅ Games refreshed: ${filteredGames.length} games from ${discoveryResult.source}`);

        } catch (error) {
            console.error('❌ Failed to refresh games:', error);
            setError(error instanceof Error ? error.message : 'Unknown error');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div
            style={{
                width: '100vw',
                height: '100vh',
                backgroundColor: '#111',
                color: '#fff',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '30px',
                fontFamily: 'sans-serif',
            }}
        >
            <h1 style={{ fontSize: '2.5rem' }}>🎮 Insolence Games Lobby</h1>

            {/* Status and Error Display */}
            {error && (
                <div style={{
                    background: 'rgba(255, 68, 68, 0.1)',
                    border: '1px solid #ff4444',
                    padding: '10px 15px',
                    borderRadius: '8px',
                    color: '#ff6666',
                    fontSize: '0.9rem',
                    marginBottom: '10px'
                }}>
                    ⚠️ {error}
                </div>
            )}

            {discoverySource === 'fallback' && !error && (
                <div style={{
                    background: 'rgba(255, 193, 7, 0.1)',
                    border: '1px solid #ffc107',
                    padding: '10px 15px',
                    borderRadius: '8px',
                    color: '#ffcc33',
                    fontSize: '0.9rem',
                    marginBottom: '10px'
                }}>
                    🔄 Using fallback game list - API temporarily unavailable
                </div>
            )}

            {discoverySource === 'api' && !loading && !error && (
                <div style={{
                    background: 'rgba(76, 175, 80, 0.1)',
                    border: '1px solid #4caf50',
                    padding: '10px 15px',
                    borderRadius: '8px',
                    color: '#66bb6a',
                    fontSize: '0.9rem',
                    marginBottom: '10px'
                }}>
                    ✅ Games loaded from API with Redis cache
                </div>
            )}

            {/* Session Status Display */}
            {sessionData && (
                <div style={{
                    background: 'rgba(255, 255, 255, 0.1)',
                    padding: '15px',
                    borderRadius: '10px',
                    marginBottom: '20px',
                    fontSize: '0.9rem',
                    color: '#333',
                    border: '1px solid #ddd'
                }}>
                    <div>🎯 Operator: {sessionData.operator_id}</div>
                    <div>💰 Currency: {sessionData.currency}</div>
                    <div>🎮 Mode: {sessionData.play_mode}</div>
                    {sessionData.balance && <div>💵 Balance: {sessionData.balance}</div>}
                    <div>🔐 Status: {sessionAuthenticated ? '✅ Authenticated' : '⚠️ Not Authenticated'}</div>
                </div>
            )}

            {loading ? (
                <div style={{ color: '#aaa', fontSize: '1.2rem', textAlign: 'center' }}>
                    <div>🔍 Loading games from Redis cache...</div>
                    <div style={{ fontSize: '0.9rem', marginTop: '10px', opacity: 0.7 }}>
                        Authenticating session and discovering available games
                    </div>
                </div>
            ) : games.length === 0 ? (
                <div style={{ color: '#ff4444', fontSize: '1.2rem' }}>
                    ❌ No games available
                </div>
            ) : (
                games.map((game) => (
                    <button
                        key={game.id}
                        onClick={() => handleEnterGame(game.id)}
                        style={{
                            padding: '20px 40px',
                            fontSize: '1.3rem',
                            borderRadius: '30px',
                            backgroundColor: game.color,
                            color: '#fff',
                            border: 'none',
                            cursor: 'pointer',
                            boxShadow: `0 4px 8px ${game.color}33`,
                            transition: 'all 0.2s ease',
                            position: 'relative',
                            overflow: 'hidden',
                        }}
                        onMouseOver={(e) => {
                            const lighterColor = game.color + '33';
                            e.currentTarget.style.backgroundColor = lighterColor.replace('33', '66');
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = `0 6px 12px ${game.color}66`;
                        }}
                        onMouseOut={(e) => {
                            e.currentTarget.style.backgroundColor = game.color;
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = `0 4px 8px ${game.color}33`;
                        }}
                    >
                        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                            <span>{game.icon} {game.name}</span>
                            {game.category && (
                                <span style={{
                                    background: game.category === '3D' ? '#4CAF50' : '#2196F3',
                                    color: '#fff',
                                    fontSize: '0.7rem',
                                    padding: '2px 6px',
                                    borderRadius: '8px',
                                    fontWeight: 'bold'
                                }}>
                                    {game.category}
                                </span>
                            )}
                        </div>

                        {game.technology && (
                            <div style={{
                                fontSize: '0.8rem',
                                opacity: 0.7,
                                marginTop: '2px'
                            }}>
                                {game.technology}
                            </div>
                        )}

                        {game.isNew && (
                            <span style={{
                                position: 'absolute',
                                top: '5px',
                                right: '10px',
                                background: '#ff4444',
                                color: '#fff',
                                fontSize: '0.7rem',
                                padding: '2px 6px',
                                borderRadius: '10px',
                                fontWeight: 'bold'
                            }}>
                                NEW
                            </span>
                        )}
                        {game.version && (
                            <span style={{
                                position: 'absolute',
                                bottom: '5px',
                                right: '10px',
                                fontSize: '0.8rem',
                                opacity: 0.6
                            }}>
                                v{game.version}
                            </span>
                        )}
                    </button>
                ))
            )}

            <div style={{
                fontSize: '0.9rem',
                color: '#aaa',
                textAlign: 'center',
                maxWidth: '600px',
                lineHeight: '1.4'
            }}>
                ✨ Dynamic multi-game architecture with automatic game discovery and true independence
                {games.length > 0 && (
                    <div style={{ marginTop: '10px', fontSize: '0.8rem' }}>
                        Found {games.length} available game{games.length !== 1 ? 's' : ''}
                        {discoverySource && ` (${discoverySource})`}
                    </div>
                )}
            </div>

            {/* Refresh Button */}
            <button
                onClick={handleRefreshGames}
                disabled={loading}
                style={{
                    padding: '10px 20px',
                    fontSize: '0.9rem',
                    borderRadius: '15px',
                    backgroundColor: loading ? '#555' : '#4caf50',
                    color: '#fff',
                    border: 'none',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    opacity: loading ? 0.6 : 1,
                    transition: 'all 0.2s ease'
                }}
            >
                {loading ? '🔄 Refreshing...' : '🔄 Refresh Games'}
            </button>

            <button
                onClick={() => window.location.href = '/ins_lootbox.html'}
                style={{
                    padding: '12px 24px',
                    fontSize: '1rem',
                    borderRadius: '20px',
                    backgroundColor: '#666',
                    color: '#fff',
                    border: 'none',
                    cursor: 'pointer',
                    opacity: 0.7,
                }}
            >
                🚪 Legacy Lootbox Game (v1.x)
            </button>
        </div>
    );
};

export default GameLobby;