/**
 * Dynamic Game Discovery Service
 * Fetches available games from the backend API with Redis cache integration
 */

export interface DynamicGameInfo {
  id: string;
  game_code: string;
  name: string;
  description: string;
  game_type: string;
  engine: string;
  status: string;
  url: string;
  color: string;
  icon: string;
  category: '2D' | '3D' | 'VR' | 'AR';
  technology: string;
  difficulty: 'Easy' | 'Medium' | 'Hard' | 'Expert';
  features: string[];
  isNew: boolean;
  version: string;
  created_at: string;
  updated_at: string;
}

export interface GameDiscoveryResult {
  success: boolean;
  games: DynamicGameInfo[];
  total_games: number;
  timestamp: string;
  source: 'api' | 'fallback';
  error?: string;
}

export class DynamicGameDiscoveryService {
  private static instance: DynamicGameDiscoveryService;
  private cache: DynamicGameInfo[] | null = null;
  private cacheTimestamp: number = 0;
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  private constructor() {}

  public static getInstance(): DynamicGameDiscoveryService {
    if (!DynamicGameDiscoveryService.instance) {
      DynamicGameDiscoveryService.instance = new DynamicGameDiscoveryService();
    }
    return DynamicGameDiscoveryService.instance;
  }

  /**
   * Get API base URL from environment
   */
  private getApiBaseUrl(): string {
    if (typeof import.meta !== 'undefined' && (import.meta as any).env) {
      const baseUrl = (import.meta as any).env.VITE_BASE_API_URL;
      if (baseUrl) return baseUrl;
    }

    if (typeof process !== 'undefined' && process.env) {
      const baseUrl = process.env.VITE_BASE_API_URL || process.env.BASE_API_ROUTE;
      if (baseUrl) return baseUrl;
    }

    return 'http://localhost:3100/api';
  }

  /**
   * Check if cache is valid
   */
  private isCacheValid(): boolean {
    return this.cache !== null && (Date.now() - this.cacheTimestamp) < this.CACHE_TTL;
  }

  /**
   * Fetch games from API
   */
  private async fetchGamesFromAPI(): Promise<DynamicGameInfo[]> {
    const apiBaseUrl = this.getApiBaseUrl();
    
    try {
      console.log('🎮 Fetching games from API:', `${apiBaseUrl}/config/all-games`);
      
      const response = await fetch(`${apiBaseUrl}/config/all-games`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(`API returned error: ${result.error || 'Unknown error'}`);
      }

      console.log(`✅ Successfully fetched ${result.total_games} games from API`);
      return result.games;
      
    } catch (error) {
      console.error('❌ Failed to fetch games from API:', error);
      throw error;
    }
  }

  /**
   * Get fallback games (static list for when API is unavailable)
   */
  private getFallbackGames(): DynamicGameInfo[] {
    console.log('⚠️ Using fallback games list');
    
    return [
      {
        id: 'ins_mines',
        game_code: 'ins_mines',
        name: 'Mines Game',
        description: '2D strategy game with configurable difficulty and real-time multipliers',
        game_type: 'strategy',
        engine: 'engine-v1',
        status: 'active',
        url: '/ins_mines.html',
        color: '#ff3366',
        icon: '💣',
        category: '2D',
        technology: 'React + TypeScript',
        difficulty: 'Medium',
        features: ['Configurable Grid', 'Real-time API', 'Multipliers', 'Strategy'],
        isNew: false,
        version: '1.5',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'ins_lootbox_base',
        game_code: 'ins_lootbox',
        name: 'Lootbox Game v2.0',
        description: '3D Lootbox Game with modular architecture, enhanced performance, and mobile optimization',
        game_type: 'lootbox',
        engine: 'engine-v1',
        status: 'active',
        url: '/ins_lootbox_base.html',
        color: '#ff6600',
        icon: '🎁',
        category: '3D',
        technology: 'Three.js + React',
        difficulty: 'Easy',
        features: ['3D Graphics', 'Animations', 'Rarity System', 'Mobile Optimized'],
        isNew: false,
        version: '2.0',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 'ins_lootbox_new',
        game_code: 'ins_lootbox2',
        name: 'Lootbox Game New',
        description: 'New variant of the lootbox game for independent customization',
        game_type: 'lootbox',
        engine: 'engine-v1',
        status: 'active',
        url: '/ins_lootbox_new.html',
        color: '#00cc66',
        icon: '🎁',
        category: '3D',
        technology: 'Three.js + React',
        difficulty: 'Easy',
        features: ['3D Graphics', 'Animations', 'Rarity System', 'New Mechanics'],
        isNew: true,
        version: '1.0',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }

  /**
   * Discover available games with caching
   */
  public async discoverGames(): Promise<GameDiscoveryResult> {
    try {
      // Return cached data if valid
      if (this.isCacheValid() && this.cache) {
        console.log('⚡ Returning cached games data');
        return {
          success: true,
          games: this.cache,
          total_games: this.cache.length,
          timestamp: new Date().toISOString(),
          source: 'api'
        };
      }

      // Try to fetch from API
      try {
        const games = await this.fetchGamesFromAPI();
        
        // Update cache
        this.cache = games;
        this.cacheTimestamp = Date.now();
        
        return {
          success: true,
          games,
          total_games: games.length,
          timestamp: new Date().toISOString(),
          source: 'api'
        };
        
      } catch (apiError) {
        console.warn('⚠️ API fetch failed, using fallback games:', apiError);
        
        // Use fallback games
        const fallbackGames = this.getFallbackGames();
        
        return {
          success: true,
          games: fallbackGames,
          total_games: fallbackGames.length,
          timestamp: new Date().toISOString(),
          source: 'fallback',
          error: apiError instanceof Error ? apiError.message : 'Unknown API error'
        };
      }
      
    } catch (error) {
      console.error('❌ Game discovery failed completely:', error);
      
      return {
        success: false,
        games: [],
        total_games: 0,
        timestamp: new Date().toISOString(),
        source: 'fallback',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get games for specific operator
   */
  public async getOperatorGames(operatorId: string): Promise<string[]> {
    try {
      const apiBaseUrl = this.getApiBaseUrl();
      
      console.log(`🎮 Fetching games for operator: ${operatorId}`);
      
      const response = await fetch(`${apiBaseUrl}/config/operator-games`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ operator_id: operatorId })
      });

      if (!response.ok) {
        throw new Error(`Operator games request failed: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(`API returned error: ${result.error || 'Unknown error'}`);
      }

      console.log(`✅ Found ${result.total_games} games for operator ${operatorId}`);
      return result.games;
      
    } catch (error) {
      console.warn(`⚠️ Failed to fetch operator games for ${operatorId}, using defaults:`, error);
      
      // Return default games for demo operator
      if (operatorId === 'demo_server') {
        return ['ins_mines', 'ins_lootbox_base', 'ins_lootbox_new'];
      }
      
      return [];
    }
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  public clearCache(): void {
    this.cache = null;
    this.cacheTimestamp = 0;
    console.log('🗑️ Game discovery cache cleared');
  }

  /**
   * Check if a specific game is available
   */
  public async isGameAvailable(gameId: string): Promise<boolean> {
    try {
      const result = await this.discoverGames();
      return result.games.some(game => game.id === gameId);
    } catch {
      return false;
    }
  }
}
